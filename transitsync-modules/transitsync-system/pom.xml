<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.transitsync</groupId>
        <artifactId>transitsync-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transitsync-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
