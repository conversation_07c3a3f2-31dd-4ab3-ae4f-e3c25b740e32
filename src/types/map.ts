/**
 * 地图相关类型定义
 */

// 基础坐标类型
export type Coordinate = [number, number] // [经度, 纬度]

// 地图样式类型
export type MapStyle = 'normal' | 'satellite' | 'dark' | 'light'

// 地图视图模式
export type ViewMode = 'normal' | 'satellite' | 'traffic'

// 基础地图标记接口
export interface BaseMarker {
  id: string
  position: Coordinate
  title: string
  description?: string
  icon?: any
  showInfo?: boolean
}

// 基础路线接口
export interface BaseRoute {
  id: string
  path: Coordinate[]
  color: string
  width?: number
  opacity?: number
  name?: string
}

// 公交站点状态
export type StationStatus = 'normal' | 'maintenance' | 'closed'

// 公交车辆状态
export type VehicleStatus = 'running' | 'stopped' | 'maintenance' | 'out_of_service'

// 路线方向
export type RouteDirection = 'up' | 'down' | 'loop'

// 公交站点接口
export interface BusStation {
  id: string
  name: string
  position: Coordinate
  routes: string[] // 经过的路线ID列表
  status: StationStatus
  address?: string
  facilities?: string[] // 站点设施
  createdAt?: string
  updatedAt?: string
}

// 公交车辆接口
export interface BusVehicle {
  id: string
  plateNumber: string // 车牌号
  routeId: string
  routeName: string
  position: Coordinate
  direction: number // 方向角度 0-360
  speed: number // 速度 km/h
  passengers: number // 当前乘客数
  capacity: number // 载客量
  status: VehicleStatus
  driverId?: string // 司机ID
  lastUpdateTime?: string // 最后更新时间
  fuel?: number // 燃油量百分比
  mileage?: number // 里程数
}

// 公交路线接口
export interface BusRoute {
  id: string
  name: string
  code?: string // 路线编号
  path: Coordinate[]
  color: string
  direction: RouteDirection
  stations: string[] // 站点ID列表（按顺序）
  distance?: number // 路线总长度（米）
  estimatedTime?: number // 预计行驶时间（分钟）
  operatingHours?: {
    start: string // 开始时间 HH:mm
    end: string // 结束时间 HH:mm
  }
  fare?: number // 票价
  vehicles?: string[] // 该路线的车辆ID列表
  isActive?: boolean // 是否启用
}

// 地图标记扩展接口（包含业务数据）
export interface MapMarker extends BaseMarker {
  type: 'station' | 'vehicle' | 'depot' | 'other'
  data?: BusStation | BusVehicle | any
  details?: Record<string, any>
}

// 地图路线扩展接口
export interface MapRoute extends BaseRoute {
  type?: 'bus_route' | 'walking' | 'driving'
  data?: BusRoute | any
}

// 地图事件接口
export interface MapEvents {
  onMapComplete?: (map: any) => void
  onMapClick?: (event: any) => void
  onMarkerClick?: (marker: MapMarker, event?: any) => void
  onRouteClick?: (route: MapRoute, event?: any) => void
  onStationClick?: (station: BusStation) => void
  onVehicleClick?: (vehicle: BusVehicle) => void
}

// 地图配置接口
export interface MapConfig {
  center: Coordinate
  zoom: number
  mapStyle: MapStyle
  showControls: boolean
  showTraffic: boolean
  showSatellite: boolean
  height: string
  apiKey?: string
  securityCode?: string
}

// 地图图层配置
export interface LayerConfig {
  stations: {
    visible: boolean
    icon?: any
    cluster?: boolean // 是否聚合
  }
  vehicles: {
    visible: boolean
    icon?: any
    showDirection?: boolean // 是否显示方向
    realtime?: boolean // 是否实时更新
  }
  routes: {
    visible: boolean
    showAll?: boolean // 是否显示所有路线
    selectedRoutes?: string[] // 选中显示的路线
  }
  traffic: {
    visible: boolean
  }
}

// 地图搜索结果
export interface SearchResult {
  id: string
  name: string
  address: string
  position: Coordinate
  type: 'station' | 'poi' | 'address'
  distance?: number // 距离当前位置的距离（米）
}

// 路径规划请求
export interface RouteRequest {
  origin: Coordinate
  destination: Coordinate
  waypoints?: Coordinate[]
  strategy?: 'fastest' | 'shortest' | 'avoid_traffic'
  mode?: 'driving' | 'walking' | 'transit'
}

// 路径规划结果
export interface RouteResult {
  path: Coordinate[]
  distance: number // 总距离（米）
  duration: number // 总时间（秒）
  steps?: RouteStep[]
  tolls?: number // 过路费
}

// 路径步骤
export interface RouteStep {
  instruction: string // 导航指令
  distance: number // 步骤距离（米）
  duration: number // 步骤时间（秒）
  path: Coordinate[]
}

// 地理围栏
export interface GeoFence {
  id: string
  name: string
  type: 'circle' | 'polygon'
  center?: Coordinate // 圆形围栏中心点
  radius?: number // 圆形围栏半径（米）
  polygon?: Coordinate[] // 多边形围栏顶点
  isActive: boolean
}

// 实时位置更新数据
export interface LocationUpdate {
  vehicleId: string
  position: Coordinate
  direction: number
  speed: number
  timestamp: number
  status?: VehicleStatus
}

// 地图统计数据
export interface MapStatistics {
  totalStations: number
  activeStations: number
  totalVehicles: number
  runningVehicles: number
  totalRoutes: number
  activeRoutes: number
  averageSpeed: number
  totalPassengers: number
}

// 热力图数据点
export interface HeatmapPoint {
  position: Coordinate
  weight: number // 权重值
  time?: string // 时间戳（用于时间热力图）
}

// 聚合点数据
export interface ClusterPoint {
  position: Coordinate
  count: number
  items: (BusStation | BusVehicle)[]
}

// 地图工具栏配置
export interface ToolbarConfig {
  showViewModeSwitch: boolean
  showTrafficToggle: boolean
  showLayerToggle: boolean
  showSearchBox: boolean
  showLocationButton: boolean
  showFullscreenButton: boolean
  customButtons?: ToolbarButton[]
}

// 工具栏按钮
export interface ToolbarButton {
  id: string
  icon: string
  text: string
  tooltip?: string
  onClick: () => void
}

// 地图主题配置
export interface MapTheme {
  name: string
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    danger: string
    info: string
  }
  styles: {
    station: any
    vehicle: any
    route: any
  }
}
