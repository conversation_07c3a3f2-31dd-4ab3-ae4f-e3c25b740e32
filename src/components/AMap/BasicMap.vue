<template>
  <div class="amap-container">
    <el-amap
      :zoom="zoom"
      :center="center"
      :map-style="mapStyle"
      class="amap-demo"
      @complete="onMapComplete"
      @click="onMapClick"
    >
      <!-- 地图控件 -->
      <el-amap-control-scale :visible="showControls" />
      <el-amap-control-tool-bar :visible="showControls" />
      <el-amap-control-control-bar :visible="showControls" />
      
      <!-- 标记点 -->
      <el-amap-marker
        v-for="marker in markers"
        :key="marker.id"
        :position="marker.position"
        :title="marker.title"
        :icon="marker.icon"
        @click="onMarkerClick(marker)"
      >
        <!-- 信息窗口 -->
        <el-amap-info-window
          v-if="marker.showInfo"
          :visible="marker.showInfo"
          @close="marker.showInfo = false"
        >
          <div class="marker-info">
            <h4>{{ marker.title }}</h4>
            <p v-if="marker.description">{{ marker.description }}</p>
            <div v-if="marker.details" class="marker-details">
              <p v-for="(value, key) in marker.details" :key="key">
                <strong>{{ key }}:</strong> {{ value }}
              </p>
            </div>
          </div>
        </el-amap-info-window>
      </el-amap-marker>
      
      <!-- 路线 -->
      <el-amap-polyline
        v-for="route in routes"
        :key="route.id"
        :path="route.path"
        :stroke-color="route.color"
        :stroke-weight="route.width || 4"
        :stroke-opacity="route.opacity || 0.8"
      />
    </el-amap>
  </div>
</template>

<script setup lang="ts">
interface MapMarker {
  id: string
  position: [number, number]
  title: string
  description?: string
  details?: Record<string, any>
  icon?: any
  showInfo?: boolean
}

interface MapRoute {
  id: string
  path: [number, number][]
  color: string
  width?: number
  opacity?: number
  name?: string
}

interface Props {
  // 地图中心点
  center?: [number, number]
  // 缩放级别
  zoom?: number
  // 地图样式
  mapStyle?: string
  // 标记点数据
  markers?: MapMarker[]
  // 路线数据
  routes?: MapRoute[]
  // 是否显示控件
  showControls?: boolean
  // 地图高度
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  center: () => [116.397428, 39.90923], // 默认北京天安门
  zoom: 13,
  mapStyle: 'normal',
  markers: () => [],
  routes: () => [],
  showControls: true,
  height: '400px'
})

const emit = defineEmits<{
  mapComplete: [map: any]
  mapClick: [event: any]
  markerClick: [marker: MapMarker, event: any]
}>()

// 响应式数据
const zoom = ref(props.zoom)
const center = ref(props.center)
const mapStyle = ref(props.mapStyle)
const markers = ref([...props.markers])
const routes = ref([...props.routes])
const showControls = ref(props.showControls)

// 监听props变化
watch(() => props.center, (newCenter) => {
  center.value = newCenter
})

watch(() => props.zoom, (newZoom) => {
  zoom.value = newZoom
})

watch(() => props.markers, (newMarkers) => {
  markers.value = [...newMarkers]
}, { deep: true })

watch(() => props.routes, (newRoutes) => {
  routes.value = [...newRoutes]
}, { deep: true })

// 地图加载完成
const onMapComplete = (map: any) => {
  console.log('地图加载完成', map)
  emit('mapComplete', map)
}

// 地图点击事件
const onMapClick = (event: any) => {
  console.log('地图点击', event)
  emit('mapClick', event)
}

// 标记点击事件
const onMarkerClick = (marker: MapMarker, event?: any) => {
  console.log('标记点击', marker)
  // 切换信息窗口显示状态
  marker.showInfo = !marker.showInfo
  emit('markerClick', marker, event)
}

// 暴露方法给父组件
defineExpose({
  // 设置地图中心点
  setCenter: (newCenter: [number, number]) => {
    center.value = newCenter
  },
  // 设置缩放级别
  setZoom: (newZoom: number) => {
    zoom.value = newZoom
  },
  // 添加标记点
  addMarker: (marker: MapMarker) => {
    markers.value.push(marker)
  },
  // 移除标记点
  removeMarker: (markerId: string) => {
    const index = markers.value.findIndex(m => m.id === markerId)
    if (index > -1) {
      markers.value.splice(index, 1)
    }
  },
  // 清空标记点
  clearMarkers: () => {
    markers.value = []
  },
  // 添加路线
  addRoute: (route: MapRoute) => {
    routes.value.push(route)
  },
  // 移除路线
  removeRoute: (routeId: string) => {
    const index = routes.value.findIndex(r => r.id === routeId)
    if (index > -1) {
      routes.value.splice(index, 1)
    }
  },
  // 清空路线
  clearRoutes: () => {
    routes.value = []
  }
})
</script>

<style scoped>
.amap-container {
  width: 100%;
  height: v-bind(height);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.amap-demo {
  width: 100%;
  height: 100%;
}

.marker-info {
  padding: 8px;
  min-width: 200px;
}

.marker-info h4 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 16px;
}

.marker-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.marker-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

.marker-details p {
  margin: 2px 0;
  font-size: 12px;
}

.marker-details strong {
  color: #303133;
}
</style>
