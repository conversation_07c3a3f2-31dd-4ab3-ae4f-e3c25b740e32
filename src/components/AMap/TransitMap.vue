<template>
  <div class="transit-map-container">
    <!-- 地图工具栏 -->
    <div class="map-toolbar">
      <el-space>
        <el-button-group>
          <el-button 
            :type="viewMode === 'normal' ? 'primary' : 'default'" 
            size="small"
            @click="setViewMode('normal')"
          >
            <el-icon><Location /></el-icon>
            普通视图
          </el-button>
          <el-button 
            :type="viewMode === 'satellite' ? 'primary' : 'default'" 
            size="small"
            @click="setViewMode('satellite')"
          >
            <el-icon><Picture /></el-icon>
            卫星视图
          </el-button>
        </el-button-group>
        
        <el-switch
          v-model="showTraffic"
          active-text="交通状况"
          @change="toggleTraffic"
        />
        
        <el-switch
          v-model="showBusStations"
          active-text="公交站点"
          @change="toggleBusStations"
        />
        
        <el-switch
          v-model="showBusRoutes"
          active-text="公交路线"
          @change="toggleBusRoutes"
        />
        
        <el-button size="small" @click="fitView">
          <el-icon><FullScreen /></el-icon>
          适应视图
        </el-button>
      </el-space>
    </div>

    <!-- 地图容器 -->
    <BasicMap
      ref="mapRef"
      :center="center"
      :zoom="zoom"
      :map-style="mapStyle"
      :markers="displayMarkers"
      :routes="displayRoutes"
      :height="height"
      @map-complete="onMapComplete"
      @map-click="onMapClick"
      @marker-click="onMarkerClick"
    />

    <!-- 图例 -->
    <div class="map-legend">
      <div class="legend-title">图例</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-icon bus-station"></div>
          <span>公交站点</span>
        </div>
        <div class="legend-item">
          <div class="legend-icon bus-vehicle"></div>
          <span>公交车辆</span>
        </div>
        <div class="legend-item">
          <div class="legend-line route-line"></div>
          <span>公交路线</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BasicMap from './BasicMap.vue'
import { Location, Picture, FullScreen } from '@element-plus/icons-vue'

interface BusStation {
  id: string
  name: string
  position: [number, number]
  routes: string[] // 经过的路线
  status: 'normal' | 'maintenance' | 'closed'
}

interface BusVehicle {
  id: string
  plateNumber: string
  routeId: string
  routeName: string
  position: [number, number]
  direction: number // 方向角度
  speed: number
  passengers: number
  capacity: number
  status: 'running' | 'stopped' | 'maintenance'
}

interface BusRoute {
  id: string
  name: string
  path: [number, number][]
  color: string
  direction: 'up' | 'down' | 'loop'
  stations: string[] // 站点ID列表
}

interface Props {
  // 地图中心点
  center?: [number, number]
  // 缩放级别
  zoom?: number
  // 地图高度
  height?: string
  // 公交站点数据
  busStations?: BusStation[]
  // 公交车辆数据
  busVehicles?: BusVehicle[]
  // 公交路线数据
  busRoutes?: BusRoute[]
}

const props = withDefaults(defineProps<Props>(), {
  center: () => [116.397428, 39.90923],
  zoom: 13,
  height: '600px',
  busStations: () => [],
  busVehicles: () => [],
  busRoutes: () => []
})

const emit = defineEmits<{
  stationClick: [station: BusStation]
  vehicleClick: [vehicle: BusVehicle]
  routeClick: [route: BusRoute]
  mapReady: [map: any]
}>()

// 响应式数据
const mapRef = ref()
const center = ref(props.center)
const zoom = ref(props.zoom)
const viewMode = ref<'normal' | 'satellite'>('normal')
const showTraffic = ref(false)
const showBusStations = ref(true)
const showBusRoutes = ref(true)
const mapInstance = ref()

// 计算属性
const mapStyle = computed(() => {
  return viewMode.value === 'satellite' ? 'satellite' : 'normal'
})

// 显示的标记点（站点 + 车辆）
const displayMarkers = computed(() => {
  const markers: any[] = []
  
  // 添加公交站点标记
  if (showBusStations.value) {
    props.busStations.forEach(station => {
      markers.push({
        id: `station-${station.id}`,
        position: station.position,
        title: station.name,
        description: `经过路线: ${station.routes.join(', ')}`,
        details: {
          '状态': station.status === 'normal' ? '正常' : 
                 station.status === 'maintenance' ? '维护中' : '关闭',
          '经过路线数': station.routes.length
        },
        icon: getStationIcon(station.status),
        showInfo: false,
        type: 'station',
        data: station
      })
    })
  }
  
  // 添加公交车辆标记
  props.busVehicles.forEach(vehicle => {
    markers.push({
      id: `vehicle-${vehicle.id}`,
      position: vehicle.position,
      title: `${vehicle.routeName} - ${vehicle.plateNumber}`,
      description: `速度: ${vehicle.speed} km/h`,
      details: {
        '车牌号': vehicle.plateNumber,
        '所属路线': vehicle.routeName,
        '当前速度': `${vehicle.speed} km/h`,
        '载客情况': `${vehicle.passengers}/${vehicle.capacity}`,
        '状态': vehicle.status === 'running' ? '运行中' : 
               vehicle.status === 'stopped' ? '停靠中' : '维护中'
      },
      icon: getBusIcon(vehicle.status),
      showInfo: false,
      type: 'vehicle',
      data: vehicle
    })
  })
  
  return markers
})

// 显示的路线
const displayRoutes = computed(() => {
  if (!showBusRoutes.value) return []
  
  return props.busRoutes.map(route => ({
    id: route.id,
    path: route.path,
    color: route.color,
    width: 4,
    opacity: 0.8,
    name: route.name,
    data: route
  }))
})

// 获取站点图标
const getStationIcon = (status: string) => {
  // 这里可以根据状态返回不同的图标
  // 实际项目中可以使用自定义图标
  return null
}

// 获取公交车图标
const getBusIcon = (status: string) => {
  // 这里可以根据状态返回不同的图标
  return null
}

// 设置视图模式
const setViewMode = (mode: 'normal' | 'satellite') => {
  viewMode.value = mode
}

// 切换交通状况
const toggleTraffic = (show: boolean) => {
  // 这里可以添加交通图层的显示/隐藏逻辑
  console.log('切换交通状况:', show)
}

// 切换公交站点显示
const toggleBusStations = (show: boolean) => {
  showBusStations.value = show
}

// 切换公交路线显示
const toggleBusRoutes = (show: boolean) => {
  showBusRoutes.value = show
}

// 适应视图
const fitView = () => {
  if (mapInstance.value && displayMarkers.value.length > 0) {
    // 这里可以调用地图的fitView方法
    console.log('适应视图')
  }
}

// 地图加载完成
const onMapComplete = (map: any) => {
  mapInstance.value = map
  emit('mapReady', map)
}

// 地图点击
const onMapClick = (event: any) => {
  console.log('地图点击:', event)
}

// 标记点击
const onMarkerClick = (marker: any) => {
  if (marker.type === 'station') {
    emit('stationClick', marker.data)
  } else if (marker.type === 'vehicle') {
    emit('vehicleClick', marker.data)
  }
}

// 暴露方法
defineExpose({
  // 定位到指定位置
  panTo: (position: [number, number]) => {
    center.value = position
  },
  // 设置缩放级别
  setZoom: (newZoom: number) => {
    zoom.value = newZoom
  },
  // 获取地图实例
  getMapInstance: () => mapInstance.value
})
</script>

<style scoped>
.transit-map-container {
  position: relative;
  width: 100%;
}

.map-toolbar {
  padding: 12px;
  background: #fff;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #ebeef5;
}

.map-legend {
  position: absolute;
  top: 60px;
  right: 12px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.legend-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
  font-size: 14px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606266;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-icon.bus-station {
  background: #409eff;
}

.legend-icon.bus-vehicle {
  background: #67c23a;
}

.legend-line {
  width: 20px;
  height: 3px;
  border-radius: 2px;
}

.legend-line.route-line {
  background: #e6a23c;
}
</style>
