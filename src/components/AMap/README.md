# 高德地图组件使用说明

本项目已集成高德地图Vue组件库 `@vuemap/vue-amap`，提供了完整的地图功能支持。

## 🚀 快速开始

### 1. 环境配置

地图API密钥已在 `.env.development` 中配置：

```env
VITE_APP_AMAP_KEY = 'd3bd2769e943741258b879e8f5c897d3'
VITE_APP_AMAP_SECURITY_CODE = 'ea1c1e43891d388a850d6a63f9e3a71b'
```

### 2. 基础使用

```vue
<template>
  <el-amap
    :zoom="13"
    :center="[116.397428, 39.90923]"
    map-style="normal"
    @complete="onMapComplete"
  >
    <el-amap-marker
      :position="[116.397428, 39.90923]"
      title="标记点"
    />
  </el-amap>
</template>

<script setup>
const onMapComplete = (map) => {
  console.log('地图加载完成', map)
}
</script>
```

## 📦 组件说明

### BasicMap 基础地图组件

位置：`src/components/AMap/BasicMap.vue`

**功能特性：**
- 支持标记点显示和交互
- 支持路线绘制
- 支持信息窗口
- 响应式数据绑定
- 丰富的事件回调

**Props：**
```typescript
interface Props {
  center?: [number, number]     // 地图中心点
  zoom?: number                 // 缩放级别
  mapStyle?: string            // 地图样式
  markers?: MapMarker[]        // 标记点数据
  routes?: MapRoute[]          // 路线数据
  showControls?: boolean       // 是否显示控件
  height?: string              // 地图高度
}
```

**使用示例：**
```vue
<BasicMap
  :center="[116.397428, 39.90923]"
  :zoom="13"
  :markers="markers"
  :routes="routes"
  height="400px"
  @map-complete="onMapComplete"
  @marker-click="onMarkerClick"
/>
```

### TransitMap 公交调度地图组件

位置：`src/components/AMap/TransitMap.vue`

**功能特性：**
- 专为公交调度系统设计
- 支持公交站点、车辆、路线显示
- 内置图例和工具栏
- 支持图层控制
- 实时数据更新

**Props：**
```typescript
interface Props {
  center?: [number, number]
  zoom?: number
  height?: string
  busStations?: BusStation[]    // 公交站点
  busVehicles?: BusVehicle[]    // 公交车辆
  busRoutes?: BusRoute[]        // 公交路线
}
```

**使用示例：**
```vue
<TransitMap
  :bus-stations="stations"
  :bus-vehicles="vehicles"
  :bus-routes="routes"
  @station-click="onStationClick"
  @vehicle-click="onVehicleClick"
/>
```

## 🛠️ 工具类

### mapUtils.ts 地图工具类

位置：`src/utils/mapUtils.ts`

**主要功能：**
- 距离计算
- 坐标转换
- 方位角计算
- 状态颜色映射
- 格式化工具

**使用示例：**
```typescript
import { calculateDistance, formatDistance, getStatusColor } from '@/utils/mapUtils'

// 计算两点距离
const distance = calculateDistance([116.1, 39.1], [116.2, 39.2])
console.log(formatDistance(distance)) // "11.1km"

// 获取状态颜色
const color = getStatusColor('running', 'vehicle') // "#52c41a"
```

## 📋 类型定义

### map.ts 类型定义

位置：`src/types/map.ts`

**主要类型：**
- `BusStation` - 公交站点
- `BusVehicle` - 公交车辆
- `BusRoute` - 公交路线
- `MapMarker` - 地图标记
- `MapRoute` - 地图路线

## 🎯 示例页面

### 基础示例
位置：`src/views/demo/map/index.vue`
- 展示基础地图和公交调度地图的使用
- 包含交互功能演示
- 提供数据管理示例

### 测试页面
位置：`src/views/demo/map-test/index.vue`
- 地图功能测试
- API调用测试
- 事件监听测试

## 🔧 高级功能

### 1. 自定义图标

```typescript
const customIcon = new AMap.Icon({
  size: new AMap.Size(25, 34),
  image: '/path/to/icon.png',
  imageSize: new AMap.Size(25, 34)
})

const marker = {
  id: '1',
  position: [116.397428, 39.90923],
  title: '自定义图标',
  icon: customIcon
}
```

### 2. 实时位置更新

```typescript
// 模拟实时更新车辆位置
const updateVehiclePosition = (vehicleId: string, newPosition: [number, number]) => {
  const vehicle = vehicles.value.find(v => v.id === vehicleId)
  if (vehicle) {
    vehicle.position = newPosition
    vehicle.lastUpdateTime = new Date().toISOString()
  }
}
```

### 3. 地理围栏

```typescript
import { isPointInPolygon } from '@/utils/mapUtils'

const fence = [
  [116.1, 39.1],
  [116.2, 39.1],
  [116.2, 39.2],
  [116.1, 39.2]
]

const isInside = isPointInPolygon([116.15, 39.15], fence)
```

## 📱 响应式支持

所有地图组件都支持响应式设计：

```scss
.map-container {
  width: 100%;
  height: 400px;
  
  @media (max-width: 768px) {
    height: 300px;
  }
}
```

## 🐛 常见问题

### 1. 地图不显示
- 检查API密钥是否正确
- 确认网络连接正常
- 查看浏览器控制台错误信息

### 2. 标记点不显示
- 检查坐标格式是否正确 `[经度, 纬度]`
- 确认标记点数据结构完整
- 检查visible属性是否为true

### 3. 事件不触发
- 确认事件名称拼写正确
- 检查组件ref是否正确绑定
- 查看组件是否已完全加载

## 🔗 相关链接

- [高德地图JavaScript API](https://lbs.amap.com/api/javascript-api/summary)
- [@vuemap/vue-amap 文档](https://github.com/yangyanggu/vue-amap)
- [Element Plus 组件库](https://element-plus.org/)

## 📄 更新日志

### v1.0.0 (2025-01-XX)
- ✅ 集成高德地图Vue组件
- ✅ 创建基础地图组件
- ✅ 创建公交调度地图组件
- ✅ 添加地图工具类
- ✅ 完善类型定义
- ✅ 提供示例页面
