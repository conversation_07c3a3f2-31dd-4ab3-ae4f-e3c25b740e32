<template>
  <div class="time-selector">
    <div class="time-selector-header">
      <h3>发车时间选择</h3>
      <el-button type="primary" size="small" @click="showAddTimeDialog = true">
        <el-icon><Plus /></el-icon>
        添加时间点
      </el-button>
    </div>
    
    <div class="time-points-container">
      <div class="time-points-grid">
        <div
          v-for="(time, index) in selectedTimes"
          :key="index"
          class="time-point-item"
          :class="{ active: activeTimeIndex === index }"
          @click="selectTime(index)"
        >
          <div class="time-display">{{ time }}</div>
          <el-button
            type="danger"
            size="small"
            circle
            class="remove-btn"
            @click.stop="removeTime(index)"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      
      <div v-if="selectedTimes.length === 0" class="empty-state">
        <el-empty description="请添加发车时间点" />
      </div>
    </div>

    <!-- 添加时间对话框 -->
    <el-dialog
      v-model="showAddTimeDialog"
      title="添加发车时间"
      width="400px"
      :before-close="handleCloseDialog"
    >
      <el-form :model="timeForm" label-width="80px">
        <el-form-item label="时间">
          <el-time-picker
            v-model="timeForm.time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="timeForm.remark"
            placeholder="可选，如：首班车、末班车"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddTimeDialog = false">取消</el-button>
        <el-button type="primary" @click="addTime">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'

// 接口定义
interface TimePoint {
  time: string
  remark?: string
}

// Props
interface Props {
  modelValue: string[]
  maxTimes?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxTimes: 50
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'timeSelected', index: number, time: string): void
  (e: 'timesChanged', times: string[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedTimes = ref<string[]>([...props.modelValue])
const activeTimeIndex = ref<number>(-1)
const showAddTimeDialog = ref(false)

const timeForm = reactive<TimePoint>({
  time: '',
  remark: ''
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  selectedTimes.value = [...newVal]
}, { deep: true })

// 监听selectedTimes变化，同步到父组件
watch(selectedTimes, (newTimes) => {
  emit('update:modelValue', newTimes)
  emit('timesChanged', newTimes)
}, { deep: true })

// 方法
const selectTime = (index: number) => {
  activeTimeIndex.value = index
  emit('timeSelected', index, selectedTimes.value[index])
}

const addTime = () => {
  if (!timeForm.time) {
    ElMessage.warning('请选择时间')
    return
  }
  
  // 检查时间是否已存在
  if (selectedTimes.value.includes(timeForm.time)) {
    ElMessage.warning('该时间点已存在')
    return
  }
  
  // 检查最大数量限制
  if (selectedTimes.value.length >= props.maxTimes) {
    ElMessage.warning(`最多只能添加${props.maxTimes}个时间点`)
    return
  }
  
  // 添加时间并排序
  selectedTimes.value.push(timeForm.time)
  selectedTimes.value.sort()
  
  // 重置表单
  timeForm.time = ''
  timeForm.remark = ''
  showAddTimeDialog.value = false
  
  ElMessage.success('时间点添加成功')
}

const removeTime = (index: number) => {
  selectedTimes.value.splice(index, 1)
  
  // 调整activeTimeIndex
  if (activeTimeIndex.value === index) {
    activeTimeIndex.value = -1
  } else if (activeTimeIndex.value > index) {
    activeTimeIndex.value--
  }
  
  ElMessage.success('时间点已删除')
}

const handleCloseDialog = () => {
  timeForm.time = ''
  timeForm.remark = ''
  showAddTimeDialog.value = false
}

// 快速添加常用时间点
const addQuickTimes = () => {
  const quickTimes = ['06:00', '06:30', '07:00', '07:30', '08:00', '18:00', '18:30', '19:00', '22:00']
  quickTimes.forEach(time => {
    if (!selectedTimes.value.includes(time)) {
      selectedTimes.value.push(time)
    }
  })
  selectedTimes.value.sort()
}

// 暴露方法给父组件
defineExpose({
  addQuickTimes,
  clearTimes: () => {
    selectedTimes.value = []
    activeTimeIndex.value = -1
  }
})
</script>

<style scoped>
.time-selector {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.time-selector-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.time-points-container {
  min-height: 120px;
}

.time-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.time-point-item {
  position: relative;
  background: #f5f7fa;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.time-point-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.time-point-item.active {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

.time-display {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  min-height: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.time-point-item:hover .remove-btn {
  opacity: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}
</style>
