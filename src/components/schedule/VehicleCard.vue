<template>
  <div
    class="vehicle-card"
    :class="{
      'is-dragging': isDragging,
      'is-assigned': vehicle.isAssigned,
      'is-disabled': vehicle.status !== '1'
    }"
    :draggable="!vehicle.isAssigned && vehicle.status === '1'"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
  >
    <!-- 车辆图标 -->
    <div class="vehicle-icon">
      <svg viewBox="0 0 24 24" class="bus-icon">
        <path d="M4,16C4,16.88 4.39,17.67 5,18.22V20A1,1 0 0,0 6,21H7A1,1 0 0,0 8,20V19H16V20A1,1 0 0,0 17,21H18A1,1 0 0,0 19,20V18.22C19.61,17.67 20,16.88 20,16V6C20,2.5 16.42,2 12,2C7.58,2 4,2.5 4,6V16M18,7H6V6A1,1 0 0,1 7,5H17A1,1 0 0,1 18,6V7M6,8H18V15H6V8M6.5,9A0.5,0.5 0 0,0 6,9.5A0.5,0.5 0 0,0 6.5,10A0.5,0.5 0 0,0 7,9.5A0.5,0.5 0 0,0 6.5,9M17.5,9A0.5,0.5 0 0,0 17,9.5A0.5,0.5 0 0,0 17.5,10A0.5,0.5 0 0,0 18,9.5A0.5,0.5 0 0,0 17.5,9M7.5,16A1.5,1.5 0 0,0 6,17.5A1.5,1.5 0 0,0 7.5,19A1.5,1.5 0 0,0 9,17.5A1.5,1.5 0 0,0 7.5,16M16.5,16A1.5,1.5 0 0,0 15,17.5A1.5,1.5 0 0,0 16.5,19A1.5,1.5 0 0,0 18,17.5A1.5,1.5 0 0,0 16.5,16Z" />
      </svg>
    </div>

    <!-- 车辆信息 -->
    <div class="vehicle-info">
      <div class="plate-number">{{ vehicle.plateNumber }}</div>
      <div class="vehicle-details">
        <span class="vehicle-number">{{ vehicle.vehicleNumber }}</span>
        <span class="vehicle-type">{{ getVehicleTypeText(vehicle.vehicleType) }}</span>
      </div>
      <div class="vehicle-status">
        <el-tag
          :type="getStatusTagType(vehicle.status)"
          size="small"
        >
          {{ getStatusText(vehicle.status) }}
        </el-tag>
      </div>
    </div>

    <!-- 拖拽提示 -->
    <div v-if="!vehicle.isAssigned && vehicle.status === '1'" class="drag-hint">
      <el-icon><Rank /></el-icon>
      <span>拖拽到时间线</span>
    </div>

    <!-- 已分配标识 -->
    <div v-if="vehicle.isAssigned" class="assigned-badge">
      <el-icon><Check /></el-icon>
      已分配
    </div>

    <!-- 不可用遮罩 -->
    <div v-if="vehicle.status !== '1'" class="disabled-overlay">
      <el-icon><Lock /></el-icon>
      <span>不可用</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Rank, Check, Lock } from '@element-plus/icons-vue'

// 接口定义
interface Vehicle {
  vehicleId: number | string
  plateNumber: string
  vehicleNumber: string
  vehicleType: string
  brandModel?: string
  seatCount?: number
  status: string // '1': 正常, '0': 停用, '2': 维修中
  isAssigned?: boolean // 是否已分配到时间线
  assignedTime?: string // 分配的时间
  totalMileage?: number
  remark?: string
}

// Props
interface Props {
  vehicle: Vehicle
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'dragStart', vehicle: Vehicle, event: DragEvent): void
  (e: 'dragEnd', vehicle: Vehicle, event: DragEvent): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isDragging = ref(false)

// 方法
const handleDragStart = (event: DragEvent) => {
  if (props.vehicle.isAssigned || props.vehicle.status !== '1') {
    event.preventDefault()
    return
  }

  isDragging.value = true
  
  // 设置拖拽数据
  const dragData = {
    type: 'vehicle',
    vehicle: props.vehicle
  }
  
  event.dataTransfer!.setData('text/plain', JSON.stringify(dragData))
  event.dataTransfer!.effectAllowed = 'move'
  
  // 设置拖拽图像
  const dragImage = event.currentTarget as HTMLElement
  event.dataTransfer!.setDragImage(dragImage, 50, 25)
  
  emit('dragStart', props.vehicle, event)
}

const handleDragEnd = (event: DragEvent) => {
  isDragging.value = false
  emit('dragEnd', props.vehicle, event)
}

// 获取车辆类型文本
const getVehicleTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'bus': '公交车',
    'electric_bus': '电动公交',
    'hybrid_bus': '混动公交',
    'minibus': '小型客车'
  }
  return typeMap[type] || type
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '0': '停用',
    '2': '维修中',
    '3': '故障'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    '1': 'success',
    '0': 'info',
    '2': 'warning',
    '3': 'danger'
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.vehicle-card {
  position: relative;
  background: #fff;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.vehicle-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.vehicle-card.is-dragging {
  opacity: 0.6;
  cursor: grabbing;
  transform: rotate(5deg);
}

.vehicle-card.is-assigned {
  border-color: #67c23a;
  background: #f0f9ff;
  cursor: default;
}

.vehicle-card.is-disabled {
  border-color: #dcdfe6;
  background: #f5f7fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.vehicle-icon {
  margin-bottom: 8px;
}

.bus-icon {
  width: 32px;
  height: 32px;
  fill: #409eff;
}

.vehicle-card.is-assigned .bus-icon {
  fill: #67c23a;
}

.vehicle-card.is-disabled .bus-icon {
  fill: #c0c4cc;
}

.vehicle-info {
  flex: 1;
  width: 100%;
}

.plate-number {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
  letter-spacing: 1px;
}

.vehicle-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.vehicle-number {
  font-weight: 600;
}

.vehicle-status {
  margin-bottom: 8px;
}

.drag-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
  margin-top: auto;
}

.assigned-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #67c23a;
  color: white;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

/* 拖拽时的样式 */
.vehicle-card[draggable="true"]:active {
  cursor: grabbing;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-card {
    min-height: 100px;
    padding: 12px;
  }
  
  .bus-icon {
    width: 24px;
    height: 24px;
  }
  
  .plate-number {
    font-size: 14px;
  }
  
  .vehicle-details {
    font-size: 11px;
  }
}
</style>
