<template>
  <div class="monitor-map-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">地图监控</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>实时监控</el-breadcrumb-item>
          <el-breadcrumb-item>地图监控</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-space>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button @click="goToTransitMap">
            <el-icon><Location /></el-icon>
            公交调度地图
          </el-button>
        </el-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon running">
                <el-icon><CaretRight /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.runningVehicles }}</div>
                <div class="stat-label">运行车辆</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon stations">
                <el-icon><LocationInformation /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeStations }}</div>
                <div class="stat-label">活跃站点</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon routes">
                <el-icon><Guide /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeRoutes }}</div>
                <div class="stat-label">运营路线</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon speed">
                <el-icon><Odometer /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.averageSpeed }}</div>
                <div class="stat-label">平均速度(km/h)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 地图容器 -->
    <div class="map-section">
      <el-card shadow="hover" class="map-card">
        <template #header>
          <div class="map-header">
            <span>实时监控地图</span>
            <div class="map-controls">
              <el-switch
                v-model="showTraffic"
                active-text="交通状况"
                @change="toggleTraffic"
              />
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="toggleAutoRefresh"
              />
              <el-select v-model="selectedRoute" placeholder="选择路线" clearable @change="onRouteChange">
                <el-option
                  v-for="route in routes"
                  :key="route.id"
                  :label="route.name"
                  :value="route.id"
                />
              </el-select>
            </div>
          </div>
        </template>

        <TransitMap
          ref="transitMapRef"
          :center="mapCenter"
          :zoom="mapZoom"
          :bus-stations="filteredStations"
          :bus-vehicles="filteredVehicles"
          :bus-routes="filteredRoutes"
          height="600px"
          @station-click="onStationClick"
          @vehicle-click="onVehicleClick"
          @map-ready="onMapReady"
        />
      </el-card>
    </div>

    <!-- 详情面板 -->
    <div class="detail-panels">
      <el-row :gutter="16">
        <!-- 选中车辆信息 -->
        <el-col :span="8">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <span>车辆信息</span>
            </template>
            <div v-if="selectedVehicle" class="vehicle-info">
              <div class="info-row">
                <span class="label">车牌号:</span>
                <span class="value">{{ selectedVehicle.plateNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">所属路线:</span>
                <span class="value">{{ selectedVehicle.routeName }}</span>
              </div>
              <div class="info-row">
                <span class="label">当前速度:</span>
                <span class="value">{{ selectedVehicle.speed }} km/h</span>
              </div>
              <div class="info-row">
                <span class="label">载客情况:</span>
                <span class="value">{{ selectedVehicle.passengers }}/{{ selectedVehicle.capacity }}</span>
              </div>
              <div class="info-row">
                <span class="label">运行状态:</span>
                <el-tag :type="getVehicleStatusType(selectedVehicle.status)">
                  {{ getVehicleStatusText(selectedVehicle.status) }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="label">最后更新:</span>
                <span class="value">{{ formatTime(selectedVehicle.lastUpdateTime) }}</span>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="请点击地图上的车辆查看详情" />
            </div>
          </el-card>
        </el-col>

        <!-- 选中站点信息 -->
        <el-col :span="8">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <span>站点信息</span>
            </template>
            <div v-if="selectedStation" class="station-info">
              <div class="info-row">
                <span class="label">站点名称:</span>
                <span class="value">{{ selectedStation.name }}</span>
              </div>
              <div class="info-row">
                <span class="label">经过路线:</span>
                <span class="value">{{ selectedStation.routes.join(', ') }}</span>
              </div>
              <div class="info-row">
                <span class="label">站点状态:</span>
                <el-tag :type="getStationStatusType(selectedStation.status)">
                  {{ getStationStatusText(selectedStation.status) }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="label">地址:</span>
                <span class="value">{{ selectedStation.address || '暂无' }}</span>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="请点击地图上的站点查看详情" />
            </div>
          </el-card>
        </el-col>

        <!-- 实时告警 -->
        <el-col :span="8">
          <el-card shadow="hover" class="detail-card">
            <template #header>
              <span>实时告警</span>
            </template>
            <div class="alert-list">
              <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="alert.level">
                <div class="alert-time">{{ formatTime(alert.time) }}</div>
                <div class="alert-message">{{ alert.message }}</div>
              </div>
              <div v-if="alerts.length === 0" class="no-data">
                <el-empty description="暂无告警信息" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="MonitorMap" lang="ts">
import TransitMap from '@/components/AMap/TransitMap.vue'
import { Refresh, Location, CaretRight, LocationInformation, Guide, Odometer } from '@element-plus/icons-vue'
import { getStatusText as getVehicleStatusText, getStatusText as getStationStatusText } from '@/utils/mapUtils'
import type { BusStation, BusVehicle, BusRoute } from '@/types/map'

// 响应式数据
const transitMapRef = ref()
const selectedVehicle = ref<BusVehicle>()
const selectedStation = ref<BusStation>()
const selectedRoute = ref('')
const showTraffic = ref(false)
const autoRefresh = ref(true)
const refreshTimer = ref()

const mapCenter = ref<[number, number]>([116.397428, 39.90923])
const mapZoom = ref(13)

// 统计数据
const stats = ref({
  runningVehicles: 0,
  activeStations: 0,
  activeRoutes: 0,
  averageSpeed: 0
})

// 模拟数据
const stations = ref<BusStation[]>([
  {
    id: 'station1',
    name: '天安门东站',
    position: [116.407428, 39.90923],
    routes: ['1路', '2路'],
    status: 'normal',
    address: '北京市东城区天安门东'
  },
  {
    id: 'station2',
    name: '王府井站',
    position: [116.417428, 39.91423],
    routes: ['1路', '5路'],
    status: 'normal',
    address: '北京市东城区王府井大街'
  }
])

const vehicles = ref<BusVehicle[]>([
  {
    id: 'bus1',
    plateNumber: '京A12345',
    routeId: 'route1',
    routeName: '1路',
    position: [116.402428, 39.90723],
    direction: 45,
    speed: 25,
    passengers: 32,
    capacity: 50,
    status: 'running',
    lastUpdateTime: new Date().toISOString()
  }
])

const routes = ref<BusRoute[]>([
  {
    id: 'route1',
    name: '1路',
    path: [
      [116.397428, 39.90923],
      [116.407428, 39.90923],
      [116.417428, 39.91423]
    ],
    color: '#1890ff',
    direction: 'up',
    stations: ['station1', 'station2']
  }
])

const alerts = ref([
  {
    id: '1',
    time: new Date().toISOString(),
    message: '1路公交车京A12345超速行驶',
    level: 'warning'
  }
])

// 计算属性
const filteredStations = computed(() => {
  if (!selectedRoute.value) return stations.value
  return stations.value.filter(station => 
    station.routes.some(route => route.includes(selectedRoute.value))
  )
})

const filteredVehicles = computed(() => {
  if (!selectedRoute.value) return vehicles.value
  return vehicles.value.filter(vehicle => vehicle.routeId === selectedRoute.value)
})

const filteredRoutes = computed(() => {
  if (!selectedRoute.value) return routes.value
  return routes.value.filter(route => route.id === selectedRoute.value)
})

// 方法
const refreshData = () => {
  // 模拟数据刷新
  stats.value = {
    runningVehicles: vehicles.value.filter(v => v.status === 'running').length,
    activeStations: stations.value.filter(s => s.status === 'normal').length,
    activeRoutes: routes.value.length,
    averageSpeed: Math.round(vehicles.value.reduce((sum, v) => sum + v.speed, 0) / vehicles.value.length)
  }
  ElMessage.success('数据已刷新')
}

const toggleTraffic = (show: boolean) => {
  console.log('切换交通状况:', show)
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer.value = setInterval(refreshData, 30000) // 30秒刷新一次
  } else {
    clearInterval(refreshTimer.value)
  }
}

const onRouteChange = (routeId: string) => {
  selectedRoute.value = routeId
  selectedVehicle.value = undefined
  selectedStation.value = undefined
}

const onStationClick = (station: BusStation) => {
  selectedStation.value = station
  selectedVehicle.value = undefined
  transitMapRef.value?.panTo(station.position)
}

const onVehicleClick = (vehicle: BusVehicle) => {
  selectedVehicle.value = vehicle
  selectedStation.value = undefined
  transitMapRef.value?.panTo(vehicle.position)
}

const onMapReady = (map: any) => {
  console.log('监控地图加载完成:', map)
}

const goToTransitMap = () => {
  router.push('/transit-monitor/map/transit')
}

const getVehicleStatusType = (status: string) => {
  switch (status) {
    case 'running': return 'success'
    case 'stopped': return 'warning'
    case 'maintenance': return 'danger'
    default: return 'info'
  }
}

const getStationStatusType = (status: string) => {
  switch (status) {
    case 'normal': return 'success'
    case 'maintenance': return 'warning'
    case 'closed': return 'danger'
    default: return 'info'
  }
}

const formatTime = (timeStr?: string) => {
  if (!timeStr) return '暂无'
  return new Date(timeStr).toLocaleString()
}

// 生命周期
onMounted(() => {
  refreshData()
  toggleAutoRefresh(true)
})

onUnmounted(() => {
  clearInterval(refreshTimer.value)
})
</script>

<style scoped>
.monitor-map-page {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.stats-cards {
  margin-bottom: 16px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.running { background: #67c23a; }
.stat-icon.stations { background: #409eff; }
.stat-icon.routes { background: #e6a23c; }
.stat-icon.speed { background: #f56c6c; }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.map-section {
  margin-bottom: 16px;
}

.map-card {
  min-height: 700px;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.detail-panels .detail-card {
  height: 400px;
}

.vehicle-info, .station-info {
  padding: 8px 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  color: #606266;
  font-weight: 500;
}

.value {
  color: #303133;
}

.alert-list {
  max-height: 320px;
  overflow-y: auto;
}

.alert-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.alert-item.warning {
  background: #fdf6ec;
  border-color: #e6a23c;
}

.alert-item.danger {
  background: #fef0f0;
  border-color: #f56c6c;
}

.alert-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 14px;
  color: #303133;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
