<template>
  <div class="transit-map-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">公交调度地图</h2>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>实时监控</el-breadcrumb-item>
          <el-breadcrumb-item>地图监控</el-breadcrumb-item>
          <el-breadcrumb-item>公交调度地图</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-space>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="openDispatchDialog">
            <el-icon><Phone /></el-icon>
            调度指令
          </el-button>
        </el-space>
      </div>
    </div>

    <!-- 全屏地图 -->
    <div class="fullscreen-map">
      <TransitMap
        ref="transitMapRef"
        :center="mapCenter"
        :zoom="mapZoom"
        :bus-stations="stations"
        :bus-vehicles="vehicles"
        :bus-routes="routes"
        height="calc(100vh - 140px)"
        @station-click="onStationClick"
        @vehicle-click="onVehicleClick"
        @map-ready="onMapReady"
      />
    </div>

    <!-- 浮动控制面板 -->
    <div class="floating-panel">
      <el-card shadow="always" class="control-panel">
        <div class="panel-header">
          <span class="panel-title">调度控制</span>
          <el-button text @click="togglePanel">
            <el-icon><Operation /></el-icon>
          </el-button>
        </div>
        
        <div v-show="panelExpanded" class="panel-content">
          <!-- 路线选择 -->
          <div class="control-group">
            <label>路线筛选:</label>
            <el-select v-model="selectedRoutes" multiple placeholder="选择路线" @change="onRouteFilter">
              <el-option
                v-for="route in routes"
                :key="route.id"
                :label="route.name"
                :value="route.id"
              />
            </el-select>
          </div>

          <!-- 车辆状态筛选 -->
          <div class="control-group">
            <label>车辆状态:</label>
            <el-checkbox-group v-model="vehicleStatusFilter" @change="onStatusFilter">
              <el-checkbox label="running">运行中</el-checkbox>
              <el-checkbox label="stopped">停靠中</el-checkbox>
              <el-checkbox label="maintenance">维护中</el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 快速操作 -->
          <div class="control-group">
            <label>快速操作:</label>
            <div class="quick-actions">
              <el-button size="small" @click="centerMap">居中显示</el-button>
              <el-button size="small" @click="showAllVehicles">显示所有车辆</el-button>
              <el-button size="small" @click="emergencyMode">紧急模式</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 调度指令对话框 -->
    <el-dialog
      v-model="dispatchDialogVisible"
      title="调度指令"
      width="600px"
      :before-close="closeDispatchDialog"
    >
      <el-form :model="dispatchForm" label-width="100px">
        <el-form-item label="目标车辆:">
          <el-select v-model="dispatchForm.vehicleId" placeholder="选择车辆">
            <el-option
              v-for="vehicle in vehicles"
              :key="vehicle.id"
              :label="`${vehicle.routeName} - ${vehicle.plateNumber}`"
              :value="vehicle.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="指令类型:">
          <el-radio-group v-model="dispatchForm.type">
            <el-radio label="speed">调整速度</el-radio>
            <el-radio label="route">路线调整</el-radio>
            <el-radio label="stop">临时停靠</el-radio>
            <el-radio label="return">返回车场</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="指令内容:">
          <el-input
            v-model="dispatchForm.content"
            type="textarea"
            :rows="3"
            placeholder="请输入具体指令内容"
          />
        </el-form-item>
        
        <el-form-item label="优先级:">
          <el-select v-model="dispatchForm.priority">
            <el-option label="普通" value="normal" />
            <el-option label="紧急" value="urgent" />
            <el-option label="特急" value="emergency" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="closeDispatchDialog">取消</el-button>
        <el-button type="primary" @click="sendDispatchCommand">发送指令</el-button>
      </template>
    </el-dialog>

    <!-- 车辆详情抽屉 -->
    <el-drawer
      v-model="vehicleDrawerVisible"
      title="车辆详情"
      direction="rtl"
      size="400px"
    >
      <div v-if="selectedVehicle" class="vehicle-details">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-item">
            <span class="label">车牌号:</span>
            <span class="value">{{ selectedVehicle.plateNumber }}</span>
          </div>
          <div class="detail-item">
            <span class="label">所属路线:</span>
            <span class="value">{{ selectedVehicle.routeName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">当前位置:</span>
            <span class="value">{{ selectedVehicle.position.join(', ') }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>运行状态</h4>
          <div class="detail-item">
            <span class="label">运行状态:</span>
            <el-tag :type="getVehicleStatusType(selectedVehicle.status)">
              {{ getVehicleStatusText(selectedVehicle.status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <span class="label">当前速度:</span>
            <span class="value">{{ selectedVehicle.speed }} km/h</span>
          </div>
          <div class="detail-item">
            <span class="label">载客情况:</span>
            <span class="value">{{ selectedVehicle.passengers }}/{{ selectedVehicle.capacity }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>调度操作</h4>
          <div class="dispatch-actions">
            <el-button type="primary" @click="quickDispatch('speed')">调整速度</el-button>
            <el-button type="warning" @click="quickDispatch('stop')">临时停靠</el-button>
            <el-button type="danger" @click="quickDispatch('return')">返回车场</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="TransitMapView" lang="ts">
import TransitMap from '@/components/AMap/TransitMap.vue'
import { ArrowLeft, Phone, Operation } from '@element-plus/icons-vue'
import { getStatusText as getVehicleStatusText } from '@/utils/mapUtils'
import type { BusStation, BusVehicle, BusRoute } from '@/types/map'

// 响应式数据
const router = useRouter()
const transitMapRef = ref()
const selectedVehicle = ref<BusVehicle>()
const selectedStation = ref<BusStation>()
const selectedRoutes = ref<string[]>([])
const vehicleStatusFilter = ref(['running', 'stopped'])
const panelExpanded = ref(true)
const dispatchDialogVisible = ref(false)
const vehicleDrawerVisible = ref(false)

const mapCenter = ref<[number, number]>([116.397428, 39.90923])
const mapZoom = ref(12)

// 调度表单
const dispatchForm = ref({
  vehicleId: '',
  type: 'speed',
  content: '',
  priority: 'normal'
})

// 模拟数据
const stations = ref<BusStation[]>([
  {
    id: 'station1',
    name: '天安门东站',
    position: [116.407428, 39.90923],
    routes: ['1路', '2路'],
    status: 'normal',
    address: '北京市东城区天安门东'
  },
  {
    id: 'station2',
    name: '王府井站',
    position: [116.417428, 39.91423],
    routes: ['1路', '5路'],
    status: 'normal',
    address: '北京市东城区王府井大街'
  },
  {
    id: 'station3',
    name: '东单站',
    position: [116.427428, 39.91923],
    routes: ['2路'],
    status: 'maintenance',
    address: '北京市东城区东单北大街'
  }
])

const vehicles = ref<BusVehicle[]>([
  {
    id: 'bus1',
    plateNumber: '京A12345',
    routeId: 'route1',
    routeName: '1路',
    position: [116.402428, 39.90723],
    direction: 45,
    speed: 25,
    passengers: 32,
    capacity: 50,
    status: 'running',
    lastUpdateTime: new Date().toISOString()
  },
  {
    id: 'bus2',
    plateNumber: '京A67890',
    routeId: 'route2',
    routeName: '2路',
    position: [116.422428, 39.91223],
    direction: 90,
    speed: 0,
    passengers: 15,
    capacity: 45,
    status: 'stopped',
    lastUpdateTime: new Date().toISOString()
  },
  {
    id: 'bus3',
    plateNumber: '京A11111',
    routeId: 'route1',
    routeName: '1路',
    position: [116.412428, 39.91023],
    direction: 180,
    speed: 30,
    passengers: 28,
    capacity: 50,
    status: 'running',
    lastUpdateTime: new Date().toISOString()
  }
])

const routes = ref<BusRoute[]>([
  {
    id: 'route1',
    name: '1路',
    path: [
      [116.397428, 39.90923],
      [116.407428, 39.90923],
      [116.417428, 39.91423],
      [116.427428, 39.91923]
    ],
    color: '#1890ff',
    direction: 'up',
    stations: ['station1', 'station2', 'station3']
  },
  {
    id: 'route2',
    name: '2路',
    path: [
      [116.397428, 39.90923],
      [116.412428, 39.91123],
      [116.427428, 39.91923]
    ],
    color: '#52c41a',
    direction: 'down',
    stations: ['station1', 'station3']
  }
])

// 方法
const goBack = () => {
  router.back()
}

const togglePanel = () => {
  panelExpanded.value = !panelExpanded.value
}

const onRouteFilter = (routeIds: string[]) => {
  console.log('路线筛选:', routeIds)
  // 这里可以实现路线筛选逻辑
}

const onStatusFilter = (statuses: string[]) => {
  console.log('状态筛选:', statuses)
  // 这里可以实现状态筛选逻辑
}

const centerMap = () => {
  transitMapRef.value?.setZoom(13)
  transitMapRef.value?.panTo(mapCenter.value)
}

const showAllVehicles = () => {
  // 计算所有车辆的边界并适应视图
  console.log('显示所有车辆')
}

const emergencyMode = () => {
  ElMessageBox.confirm('确定要启用紧急模式吗？', '紧急模式', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('紧急模式已启用')
  })
}

const onStationClick = (station: BusStation) => {
  selectedStation.value = station
  ElMessage.info(`选择了站点: ${station.name}`)
}

const onVehicleClick = (vehicle: BusVehicle) => {
  selectedVehicle.value = vehicle
  vehicleDrawerVisible.value = true
}

const onMapReady = (map: any) => {
  console.log('公交调度地图加载完成:', map)
}

const openDispatchDialog = () => {
  dispatchDialogVisible.value = true
}

const closeDispatchDialog = () => {
  dispatchDialogVisible.value = false
  dispatchForm.value = {
    vehicleId: '',
    type: 'speed',
    content: '',
    priority: 'normal'
  }
}

const sendDispatchCommand = () => {
  if (!dispatchForm.value.vehicleId || !dispatchForm.value.content) {
    ElMessage.warning('请填写完整的调度信息')
    return
  }
  
  // 模拟发送调度指令
  ElMessage.success('调度指令已发送')
  closeDispatchDialog()
}

const quickDispatch = (type: string) => {
  if (!selectedVehicle.value) return
  
  dispatchForm.value.vehicleId = selectedVehicle.value.id
  dispatchForm.value.type = type
  
  switch (type) {
    case 'speed':
      dispatchForm.value.content = '请调整行驶速度'
      break
    case 'stop':
      dispatchForm.value.content = '请在下一站临时停靠'
      break
    case 'return':
      dispatchForm.value.content = '请返回车场'
      dispatchForm.value.priority = 'urgent'
      break
  }
  
  vehicleDrawerVisible.value = false
  dispatchDialogVisible.value = true
}

const getVehicleStatusType = (status: string) => {
  switch (status) {
    case 'running': return 'success'
    case 'stopped': return 'warning'
    case 'maintenance': return 'danger'
    default: return 'info'
  }
}

// 生命周期
onMounted(() => {
  // 初始化选择所有路线
  selectedRoutes.value = routes.value.map(r => r.id)
})
</script>

<style scoped>
.transit-map-page {
  padding: 16px;
  height: 100vh;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-title {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.fullscreen-map {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.floating-panel {
  position: fixed;
  top: 200px;
  right: 20px;
  width: 300px;
  z-index: 1000;
}

.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-title {
  font-weight: 600;
  color: #303133;
}

.control-group {
  margin-bottom: 16px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vehicle-details {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
}

.detail-item .label {
  color: #606266;
  font-weight: 500;
}

.detail-item .value {
  color: #303133;
}

.dispatch-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
