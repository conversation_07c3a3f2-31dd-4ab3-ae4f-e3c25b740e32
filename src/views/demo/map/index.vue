<template>
  <div class="p-4">
    <el-card shadow="hover" class="mb-4">
      <template #header>
        <div class="card-header">
          <span class="text-lg font-semibold">高德地图集成示例</span>
          <el-space>
            <el-button type="primary" @click="addRandomBus">添加随机公交</el-button>
            <el-button type="success" @click="addRandomStation">添加随机站点</el-button>
            <el-button @click="clearAll">清空所有</el-button>
          </el-space>
        </div>
      </template>

      <!-- 基础地图示例 -->
      <div class="mb-6">
        <h3 class="mb-3">基础地图组件</h3>
        <BasicMap
          ref="basicMapRef"
          :center="[116.397428, 39.90923]"
          :zoom="13"
          :markers="basicMarkers"
          :routes="basicRoutes"
          height="400px"
          @map-complete="onBasicMapComplete"
          @marker-click="onBasicMarkerClick"
        />
      </div>

      <!-- 公交调度地图示例 -->
      <div>
        <h3 class="mb-3">公交调度地图组件</h3>
        <TransitMap
          ref="transitMapRef"
          :center="[116.397428, 39.90923]"
          :zoom="13"
          :bus-stations="busStations"
          :bus-vehicles="busVehicles"
          :bus-routes="busRoutes"
          height="600px"
          @station-click="onStationClick"
          @vehicle-click="onVehicleClick"
          @map-ready="onTransitMapReady"
        />
      </div>
    </el-card>

    <!-- 信息面板 -->
    <el-row :gutter="16">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>公交站点 ({{ busStations.length }})</span>
          </template>
          <div class="info-list">
            <div 
              v-for="station in busStations" 
              :key="station.id"
              class="info-item"
              :class="{ active: selectedStation?.id === station.id }"
              @click="selectStation(station)"
            >
              <div class="info-title">{{ station.name }}</div>
              <div class="info-desc">经过 {{ station.routes.length }} 条路线</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>公交车辆 ({{ busVehicles.length }})</span>
          </template>
          <div class="info-list">
            <div 
              v-for="vehicle in busVehicles" 
              :key="vehicle.id"
              class="info-item"
              :class="{ active: selectedVehicle?.id === vehicle.id }"
              @click="selectVehicle(vehicle)"
            >
              <div class="info-title">{{ vehicle.plateNumber }}</div>
              <div class="info-desc">{{ vehicle.routeName }} - {{ vehicle.speed }}km/h</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <span>公交路线 ({{ busRoutes.length }})</span>
          </template>
          <div class="info-list">
            <div 
              v-for="route in busRoutes" 
              :key="route.id"
              class="info-item"
              @click="selectRoute(route)"
            >
              <div class="info-title">{{ route.name }}</div>
              <div class="info-desc">{{ route.stations.length }} 个站点</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="MapDemo" lang="ts">
import BasicMap from '@/components/AMap/BasicMap.vue'
import TransitMap from '@/components/AMap/TransitMap.vue'

// 基础地图数据
const basicMapRef = ref()
const basicMarkers = ref([
  {
    id: '1',
    position: [116.397428, 39.90923] as [number, number],
    title: '天安门',
    description: '北京市中心',
    showInfo: false
  },
  {
    id: '2',
    position: [116.405285, 39.904989] as [number, number],
    title: '故宫',
    description: '明清皇宫',
    showInfo: false
  }
])

const basicRoutes = ref([
  {
    id: 'route1',
    path: [
      [116.397428, 39.90923],
      [116.405285, 39.904989],
      [116.423332, 39.907445]
    ] as [number, number][],
    color: '#1890ff',
    name: '示例路线'
  }
])

// 公交调度地图数据
const transitMapRef = ref()
const selectedStation = ref()
const selectedVehicle = ref()

// 公交站点数据
const busStations = ref([
  {
    id: 'station1',
    name: '天安门东站',
    position: [116.407428, 39.90923] as [number, number],
    routes: ['1路', '2路', '10路'],
    status: 'normal' as const
  },
  {
    id: 'station2',
    name: '王府井站',
    position: [116.417428, 39.91423] as [number, number],
    routes: ['1路', '5路'],
    status: 'normal' as const
  },
  {
    id: 'station3',
    name: '东单站',
    position: [116.427428, 39.91923] as [number, number],
    routes: ['2路', '10路'],
    status: 'maintenance' as const
  }
])

// 公交车辆数据
const busVehicles = ref([
  {
    id: 'bus1',
    plateNumber: '京A12345',
    routeId: 'route1',
    routeName: '1路',
    position: [116.402428, 39.90723] as [number, number],
    direction: 45,
    speed: 25,
    passengers: 32,
    capacity: 50,
    status: 'running' as const
  },
  {
    id: 'bus2',
    plateNumber: '京A67890',
    routeId: 'route2',
    routeName: '2路',
    position: [116.422428, 39.91223] as [number, number],
    direction: 90,
    speed: 0,
    passengers: 15,
    capacity: 45,
    status: 'stopped' as const
  }
])

// 公交路线数据
const busRoutes = ref([
  {
    id: 'route1',
    name: '1路',
    path: [
      [116.397428, 39.90923],
      [116.407428, 39.90923],
      [116.417428, 39.91423],
      [116.427428, 39.91923]
    ] as [number, number][],
    color: '#1890ff',
    direction: 'up' as const,
    stations: ['station1', 'station2', 'station3']
  },
  {
    id: 'route2',
    name: '2路',
    path: [
      [116.397428, 39.90923],
      [116.412428, 39.91123],
      [116.427428, 39.91923]
    ] as [number, number][],
    color: '#52c41a',
    direction: 'down' as const,
    stations: ['station1', 'station3']
  }
])

// 事件处理
const onBasicMapComplete = (map: any) => {
  console.log('基础地图加载完成:', map)
}

const onBasicMarkerClick = (marker: any) => {
  console.log('基础地图标记点击:', marker)
  ElMessage.success(`点击了标记: ${marker.title}`)
}

const onTransitMapReady = (map: any) => {
  console.log('公交地图加载完成:', map)
}

const onStationClick = (station: any) => {
  console.log('站点点击:', station)
  selectedStation.value = station
  ElMessage.info(`选择了站点: ${station.name}`)
}

const onVehicleClick = (vehicle: any) => {
  console.log('车辆点击:', vehicle)
  selectedVehicle.value = vehicle
  ElMessage.info(`选择了车辆: ${vehicle.plateNumber}`)
}

// 工具方法
const selectStation = (station: any) => {
  selectedStation.value = station
  // 地图定位到站点
  transitMapRef.value?.panTo(station.position)
}

const selectVehicle = (vehicle: any) => {
  selectedVehicle.value = vehicle
  // 地图定位到车辆
  transitMapRef.value?.panTo(vehicle.position)
}

const selectRoute = (route: any) => {
  console.log('选择路线:', route)
  ElMessage.info(`选择了路线: ${route.name}`)
}

// 添加随机数据
const addRandomBus = () => {
  const newBus = {
    id: `bus${Date.now()}`,
    plateNumber: `京A${Math.floor(Math.random() * 90000) + 10000}`,
    routeId: 'route1',
    routeName: '1路',
    position: [
      116.397428 + (Math.random() - 0.5) * 0.1,
      39.90923 + (Math.random() - 0.5) * 0.1
    ] as [number, number],
    direction: Math.floor(Math.random() * 360),
    speed: Math.floor(Math.random() * 40),
    passengers: Math.floor(Math.random() * 50),
    capacity: 50,
    status: 'running' as const
  }
  busVehicles.value.push(newBus)
  ElMessage.success('添加了新的公交车辆')
}

const addRandomStation = () => {
  const newStation = {
    id: `station${Date.now()}`,
    name: `随机站点${busStations.value.length + 1}`,
    position: [
      116.397428 + (Math.random() - 0.5) * 0.1,
      39.90923 + (Math.random() - 0.5) * 0.1
    ] as [number, number],
    routes: ['1路'],
    status: 'normal' as const
  }
  busStations.value.push(newStation)
  ElMessage.success('添加了新的公交站点')
}

const clearAll = () => {
  busVehicles.value = []
  busStations.value = []
  selectedStation.value = null
  selectedVehicle.value = null
  ElMessage.info('已清空所有数据')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-list {
  max-height: 300px;
  overflow-y: auto;
}

.info-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 4px;
}

.info-item:hover {
  background-color: #f5f7fa;
}

.info-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.info-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.info-desc {
  font-size: 12px;
  color: #909399;
}
</style>
