<template>
  <div class="p-4">
    <el-card shadow="hover">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">高德地图测试页面</span>
          <el-space>
            <el-tag :type="mapLoaded ? 'success' : 'warning'">
              {{ mapLoaded ? '地图已加载' : '地图加载中...' }}
            </el-tag>
            <el-button @click="testBasicFeatures">测试基础功能</el-button>
          </el-space>
        </div>
      </template>

      <!-- 基础地图测试 -->
      <div class="mb-6">
        <h3 class="mb-3">基础地图测试</h3>
        <div class="map-container">
          <el-amap
            :zoom="13"
            :center="[116.397428, 39.90923]"
            map-style="normal"
            class="test-map"
            @complete="onMapComplete"
            @click="onMapClick"
          >
            <!-- 基础控件 -->
            <el-amap-control-scale />
            <el-amap-control-tool-bar />
            
            <!-- 测试标记 -->
            <el-amap-marker
              :position="[116.397428, 39.90923]"
              title="天安门"
              @click="onMarkerClick"
            >
              <el-amap-info-window :visible="showInfoWindow">
                <div class="p-2">
                  <h4>天安门</h4>
                  <p>北京市中心地标建筑</p>
                  <el-button size="small" @click="showInfoWindow = false">关闭</el-button>
                </div>
              </el-amap-info-window>
            </el-amap-marker>
            
            <!-- 测试路线 -->
            <el-amap-polyline
              :path="testRoute"
              stroke-color="#1890ff"
              :stroke-weight="4"
            />
          </el-amap>
        </div>
      </div>

      <!-- 功能测试面板 -->
      <div class="test-panel">
        <h3 class="mb-3">功能测试</h3>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-card shadow="never" class="test-card">
              <h4>地图信息</h4>
              <div class="info-item">
                <span>中心点:</span>
                <span>{{ mapCenter.join(', ') }}</span>
              </div>
              <div class="info-item">
                <span>缩放级别:</span>
                <span>{{ mapZoom }}</span>
              </div>
              <div class="info-item">
                <span>地图样式:</span>
                <span>{{ mapStyle }}</span>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="never" class="test-card">
              <h4>交互测试</h4>
              <div class="info-item">
                <span>点击次数:</span>
                <span>{{ clickCount }}</span>
              </div>
              <div class="info-item">
                <span>最后点击位置:</span>
                <span>{{ lastClickPosition.join(', ') }}</span>
              </div>
              <el-button size="small" @click="resetStats">重置统计</el-button>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="never" class="test-card">
              <h4>API测试</h4>
              <div class="test-buttons">
                <el-button size="small" @click="changeMapStyle">切换样式</el-button>
                <el-button size="small" @click="addRandomMarker">添加标记</el-button>
                <el-button size="small" @click="clearMarkers">清空标记</el-button>
                <el-button size="small" @click="fitView">适应视图</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 日志面板 -->
      <div class="log-panel mt-4">
        <h3 class="mb-3">操作日志</h3>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="MapTest" lang="ts">
interface LogItem {
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

// 响应式数据
const mapLoaded = ref(false)
const showInfoWindow = ref(false)
const mapCenter = ref([116.397428, 39.90923])
const mapZoom = ref(13)
const mapStyle = ref('normal')
const clickCount = ref(0)
const lastClickPosition = ref([0, 0])
const logs = ref<LogItem[]>([])
const mapInstance = ref()

// 测试路线数据
const testRoute = ref([
  [116.397428, 39.90923],
  [116.405285, 39.904989],
  [116.423332, 39.907445],
  [116.431428, 39.912445]
])

// 添加日志
const addLog = (message: string, type: LogItem['type'] = 'info') => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.unshift({ time, message, type })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 地图加载完成
const onMapComplete = (map: any) => {
  console.log('地图加载完成:', map)
  mapInstance.value = map
  mapLoaded.value = true
  addLog('地图加载完成', 'success')
  
  // 监听地图事件
  map.on('zoomchange', () => {
    mapZoom.value = map.getZoom()
    addLog(`缩放级别变更: ${mapZoom.value}`)
  })
  
  map.on('moveend', () => {
    const center = map.getCenter()
    mapCenter.value = [center.lng, center.lat]
    addLog(`地图中心变更: ${mapCenter.value.join(', ')}`)
  })
}

// 地图点击事件
const onMapClick = (event: any) => {
  clickCount.value++
  const { lng, lat } = event.lnglat
  lastClickPosition.value = [lng, lat]
  addLog(`地图点击: ${lng.toFixed(6)}, ${lat.toFixed(6)}`)
}

// 标记点击事件
const onMarkerClick = () => {
  showInfoWindow.value = true
  addLog('标记点击: 天安门', 'info')
}

// 测试基础功能
const testBasicFeatures = () => {
  addLog('开始测试基础功能...', 'info')
  
  if (!mapInstance.value) {
    addLog('地图未加载，无法测试', 'error')
    return
  }
  
  // 测试地图方法
  try {
    const zoom = mapInstance.value.getZoom()
    const center = mapInstance.value.getCenter()
    addLog(`当前缩放级别: ${zoom}`, 'success')
    addLog(`当前中心点: ${center.lng.toFixed(6)}, ${center.lat.toFixed(6)}`, 'success')
    
    // 测试设置中心点
    mapInstance.value.setCenter([116.407428, 39.91423])
    addLog('设置新的中心点', 'success')
    
    setTimeout(() => {
      mapInstance.value.setCenter([116.397428, 39.90923])
      addLog('恢复原始中心点', 'success')
    }, 2000)
    
  } catch (error) {
    addLog(`测试失败: ${error}`, 'error')
  }
}

// 切换地图样式
const changeMapStyle = () => {
  if (!mapInstance.value) return
  
  const styles = ['normal', 'satellite']
  const currentIndex = styles.indexOf(mapStyle.value)
  const nextIndex = (currentIndex + 1) % styles.length
  const newStyle = styles[nextIndex]
  
  mapStyle.value = newStyle
  mapInstance.value.setMapStyle(newStyle)
  addLog(`切换地图样式: ${newStyle}`)
}

// 添加随机标记
const addRandomMarker = () => {
  if (!mapInstance.value) return
  
  const randomLng = 116.397428 + (Math.random() - 0.5) * 0.1
  const randomLat = 39.90923 + (Math.random() - 0.5) * 0.1
  
  const marker = new AMap.Marker({
    position: [randomLng, randomLat],
    title: `随机标记 ${Date.now()}`
  })
  
  marker.setMap(mapInstance.value)
  addLog(`添加随机标记: ${randomLng.toFixed(6)}, ${randomLat.toFixed(6)}`, 'success')
}

// 清空标记
const clearMarkers = () => {
  if (!mapInstance.value) return
  
  mapInstance.value.clearMap()
  addLog('清空所有标记', 'warning')
}

// 适应视图
const fitView = () => {
  if (!mapInstance.value) return
  
  mapInstance.value.setFitView()
  addLog('适应视图', 'info')
}

// 重置统计
const resetStats = () => {
  clickCount.value = 0
  lastClickPosition.value = [0, 0]
  logs.value = []
  addLog('统计数据已重置', 'info')
}

// 页面加载时添加初始日志
onMounted(() => {
  addLog('页面加载完成，等待地图初始化...', 'info')
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-map {
  width: 100%;
  height: 100%;
}

.test-panel {
  margin-top: 20px;
}

.test-card {
  height: 160px;
}

.test-card h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item span:first-child {
  color: #606266;
}

.info-item span:last-child {
  color: #303133;
  font-weight: 500;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-panel {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.5;
}

.log-time {
  color: #909399;
  margin-right: 12px;
  min-width: 80px;
}

.log-message {
  color: #303133;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.info .log-message {
  color: #409eff;
}
</style>
