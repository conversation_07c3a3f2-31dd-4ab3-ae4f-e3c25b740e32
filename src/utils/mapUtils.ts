/**
 * 地图工具类
 * 提供地图相关的工具方法和常量
 */

// 地图样式常量
export const MAP_STYLES = {
  NORMAL: 'normal',
  SATELLITE: 'satellite',
  DARK: 'dark',
  LIGHT: 'light'
} as const

// 地图图标常量
export const MAP_ICONS = {
  BUS_STATION: 'bus-station',
  BUS_VEHICLE: 'bus-vehicle',
  BUS_DEPOT: 'bus-depot',
  TRAFFIC_LIGHT: 'traffic-light'
} as const

// 公交车辆状态
export const BUS_STATUS = {
  RUNNING: 'running',
  STOPPED: 'stopped',
  MAINTENANCE: 'maintenance',
  OUT_OF_SERVICE: 'out_of_service'
} as const

// 公交站点状态
export const STATION_STATUS = {
  NORMAL: 'normal',
  MAINTENANCE: 'maintenance',
  CLOSED: 'closed'
} as const

// 路线颜色配置
export const ROUTE_COLORS = [
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#faad14', // 黄色
  '#f5222d', // 红色
  '#722ed1', // 紫色
  '#fa541c', // 橙色
  '#13c2c2', // 青色
  '#eb2f96'  // 粉色
]

/**
 * 计算两点之间的距离（米）
 * @param point1 起点坐标 [lng, lat]
 * @param point2 终点坐标 [lng, lat]
 * @returns 距离（米）
 */
export function calculateDistance(
  point1: [number, number], 
  point2: [number, number]
): number {
  const [lng1, lat1] = point1
  const [lng2, lat2] = point2
  
  const R = 6371000 // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  
  return R * c
}

/**
 * 计算路径的总长度（米）
 * @param path 路径坐标数组
 * @returns 总长度（米）
 */
export function calculatePathLength(path: [number, number][]): number {
  if (path.length < 2) return 0
  
  let totalLength = 0
  for (let i = 1; i < path.length; i++) {
    totalLength += calculateDistance(path[i - 1], path[i])
  }
  
  return totalLength
}

/**
 * 计算两点之间的方位角（度）
 * @param point1 起点坐标 [lng, lat]
 * @param point2 终点坐标 [lng, lat]
 * @returns 方位角（0-360度）
 */
export function calculateBearing(
  point1: [number, number], 
  point2: [number, number]
): number {
  const [lng1, lat1] = point1
  const [lng2, lat2] = point2
  
  const dLng = (lng2 - lng1) * Math.PI / 180
  const lat1Rad = lat1 * Math.PI / 180
  const lat2Rad = lat2 * Math.PI / 180
  
  const y = Math.sin(dLng) * Math.cos(lat2Rad)
  const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
    Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng)
  
  let bearing = Math.atan2(y, x) * 180 / Math.PI
  bearing = (bearing + 360) % 360
  
  return bearing
}

/**
 * 判断点是否在多边形内
 * @param point 点坐标 [lng, lat]
 * @param polygon 多边形坐标数组
 * @returns 是否在多边形内
 */
export function isPointInPolygon(
  point: [number, number], 
  polygon: [number, number][]
): boolean {
  const [x, y] = point
  let inside = false
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i]
    const [xj, yj] = polygon[j]
    
    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside
    }
  }
  
  return inside
}

/**
 * 获取路径的边界框
 * @param path 路径坐标数组
 * @returns 边界框 {minLng, minLat, maxLng, maxLat}
 */
export function getPathBounds(path: [number, number][]) {
  if (path.length === 0) return null
  
  let minLng = path[0][0]
  let maxLng = path[0][0]
  let minLat = path[0][1]
  let maxLat = path[0][1]
  
  path.forEach(([lng, lat]) => {
    minLng = Math.min(minLng, lng)
    maxLng = Math.max(maxLng, lng)
    minLat = Math.min(minLat, lat)
    maxLat = Math.max(maxLat, lat)
  })
  
  return { minLng, minLat, maxLng, maxLat }
}

/**
 * 格式化距离显示
 * @param distance 距离（米）
 * @returns 格式化的距离字符串
 */
export function formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else {
    return `${(distance / 1000).toFixed(1)}km`
  }
}

/**
 * 格式化速度显示
 * @param speed 速度（km/h）
 * @returns 格式化的速度字符串
 */
export function formatSpeed(speed: number): string {
  return `${speed}km/h`
}

/**
 * 获取状态对应的颜色
 * @param status 状态
 * @param type 类型（vehicle | station）
 * @returns 颜色值
 */
export function getStatusColor(status: string, type: 'vehicle' | 'station'): string {
  if (type === 'vehicle') {
    switch (status) {
      case BUS_STATUS.RUNNING:
        return '#52c41a' // 绿色
      case BUS_STATUS.STOPPED:
        return '#faad14' // 黄色
      case BUS_STATUS.MAINTENANCE:
        return '#f5222d' // 红色
      case BUS_STATUS.OUT_OF_SERVICE:
        return '#d9d9d9' // 灰色
      default:
        return '#1890ff' // 默认蓝色
    }
  } else {
    switch (status) {
      case STATION_STATUS.NORMAL:
        return '#1890ff' // 蓝色
      case STATION_STATUS.MAINTENANCE:
        return '#faad14' // 黄色
      case STATION_STATUS.CLOSED:
        return '#f5222d' // 红色
      default:
        return '#1890ff' // 默认蓝色
    }
  }
}

/**
 * 获取状态对应的文本
 * @param status 状态
 * @param type 类型（vehicle | station）
 * @returns 状态文本
 */
export function getStatusText(status: string, type: 'vehicle' | 'station'): string {
  if (type === 'vehicle') {
    switch (status) {
      case BUS_STATUS.RUNNING:
        return '运行中'
      case BUS_STATUS.STOPPED:
        return '停靠中'
      case BUS_STATUS.MAINTENANCE:
        return '维护中'
      case BUS_STATUS.OUT_OF_SERVICE:
        return '停运'
      default:
        return '未知'
    }
  } else {
    switch (status) {
      case STATION_STATUS.NORMAL:
        return '正常'
      case STATION_STATUS.MAINTENANCE:
        return '维护中'
      case STATION_STATUS.CLOSED:
        return '关闭'
      default:
        return '未知'
    }
  }
}

/**
 * 生成随机坐标（在指定中心点附近）
 * @param center 中心点坐标 [lng, lat]
 * @param radius 半径（度）
 * @returns 随机坐标
 */
export function generateRandomCoordinate(
  center: [number, number], 
  radius: number = 0.01
): [number, number] {
  const [lng, lat] = center
  const randomLng = lng + (Math.random() - 0.5) * radius * 2
  const randomLat = lat + (Math.random() - 0.5) * radius * 2
  return [randomLng, randomLat]
}

/**
 * 坐标系转换工具
 */
export class CoordinateConverter {
  // WGS84 转 GCJ02 (GPS坐标转高德坐标)
  static wgs84ToGcj02(lng: number, lat: number): [number, number] {
    const PI = 3.1415926535897932384626
    const a = 6378245.0
    const ee = 0.00669342162296594323
    
    let dLat = this.transformLat(lng - 105.0, lat - 35.0)
    let dLng = this.transformLng(lng - 105.0, lat - 35.0)
    const radLat = lat / 180.0 * PI
    let magic = Math.sin(radLat)
    magic = 1 - ee * magic * magic
    const sqrtMagic = Math.sqrt(magic)
    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * PI)
    dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * PI)
    
    return [lng + dLng, lat + dLat]
  }
  
  private static transformLat(lng: number, lat: number): number {
    const PI = 3.1415926535897932384626
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
    ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
    return ret
  }
  
  private static transformLng(lng: number, lat: number): number {
    const PI = 3.1415926535897932384626
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
    ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
    ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
    return ret
  }
}
