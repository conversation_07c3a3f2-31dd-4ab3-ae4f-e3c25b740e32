# 开发规范和规则

- Maven依赖修复：SMS模块sms4j依赖groupId从com.transitsync.sms4j改为org.dromara.sms4j，工作流模块warm-flow依赖groupId从com.transitsync.warm改为org.dromara.warm
- Redis配置修复：开发环境application-dev.yml中注释掉空的password配置，避免Redisson尝试AUTH认证导致连接失败
- Spring依赖注入修复：为DeptNameTranslationImpl、UserNameTranslationImpl、NicknameTranslationImpl添加@ConditionalOnBean条件注解，避免在对应Service未加载时出现依赖注入失败
- 包名修改问题：启动类从org.dromara包改为com.transitsync包后，Spring Boot默认组件扫描无法扫描到org.dromara包下的Service实现类，需要在@SpringBootApplication注解中添加scanBasePackages配置
- Element Plus Icons修复：Truck图标不存在，已替换为Van图标。在schedule/plan模块中所有车辆相关图标使用Van替代Truck
- 排班逻辑优化：车辆可以一天内跑多趟，移除isAssigned单一状态限制，改为基于时间冲突检测的多班次分配系统
