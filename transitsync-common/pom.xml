<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transitsync-backend</artifactId>
        <groupId>com.transitsync</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>transitsync-common-bom</module>
        <module>transitsync-common-social</module>
        <module>transitsync-common-core</module>
        <module>transitsync-common-doc</module>
        <module>transitsync-common-excel</module>
        <module>transitsync-common-idempotent</module>
        <module>transitsync-common-job</module>
        <module>transitsync-common-log</module>
        <module>transitsync-common-mail</module>
        <module>transitsync-common-mybatis</module>
        <module>transitsync-common-oss</module>
        <module>transitsync-common-ratelimiter</module>
        <module>transitsync-common-redis</module>
        <module>transitsync-common-satoken</module>
        <module>transitsync-common-security</module>
        <module>transitsync-common-sms</module>
        <module>transitsync-common-web</module>
        <module>transitsync-common-translation</module>
        <module>transitsync-common-sensitive</module>
        <module>transitsync-common-json</module>
        <module>transitsync-common-encrypt</module>
        <module>transitsync-common-tenant</module>
        <module>transitsync-common-websocket</module>
        <module>transitsync-common-sse</module>
    </modules>

    <artifactId>transitsync-common</artifactId>
    <packaging>pom</packaging>

    <description>
        TransitSync 通用模块
    </description>

</project>
